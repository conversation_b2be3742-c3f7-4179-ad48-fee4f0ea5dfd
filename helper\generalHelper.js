const constant = require('../config/constant');
const crypto = require('crypto');
const algorithm = 'aes-256-cbc';
const key = crypto.randomBytes(32);
const iv = crypto.randomBytes(16);
const AWS = require('aws-sdk');
const jwt = require('jsonwebtoken');
require('dotenv').config();
const {Op} = require('sequelize');
const moment = require('moment');
const UserAdsAnalyticModel = require('../database/models').user_ads_analytic;
const barModel = require('../database/models').bar;
const AdsModel = require('../database/models').ads;
module.exports = {
	generateOtp() {
		otp = Math.floor(100000 + Math.random() * 900000);
		return otp;
	},
	generateRandomString(length = 8) {
		var result = '';
		var characters =
			'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		var charactersLength = characters.length;
		for (var i = 0; i < length; i++) {
			result += characters.charAt(
				Math.floor(Math.random() * charactersLength)
			);
		}
		return result;
	},
	async createorderrandomnumber(adsData) {
		var min = 1000
		var max = 9999
		var randomNum = Math.floor(Math.random() * (max - min + 1)) + min

		// if (adsData) {
		// 	var invoice_no = 'MYTAB' + randomNum
		// } else {
		// 	var invoice_no = randomNum
		// }
		var invoice_no = 'MYTAB' + randomNum

		const adsDetails = await AdsModel.findAll({
			attributes: ['invoice_no'],
			where: {
				invoice_no: invoice_no
			}
		})
		if (adsDetails.length > 0) {
			return createorderrandomnumber(adsData)
		} else {
			return invoice_no
		}
	},
	generateAutomaticId(type, name, id) {
		name = name.substring(0, 4);
		let prefix;
		if (type === constant.USER) {
			prefix = 'U';
		} else if (type === constant.RD) {
			prefix = 'R';
		} else if (type === constant.GROUP) {
			prefix = 'G';
		}

		return prefix + name + id;
	},
	async convertUTCToVenueTimezone(barID, utcDate) {
		const venueTimezone = await barModel.findOne({
			where: {
				id: barID
			},
			attributes: ['timezone']
		});
		const convertedDate = moment.tz(utcDate, 'UTC').tz(venueTimezone.timezone).format('YYYY-MM-DD');
		return convertedDate;
	},	
	encrypt(text) {
		let cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key), iv);
		let encrypted = cipher.update(text);
		encrypted = Buffer.concat([encrypted, cipher.final()]);
		return {
			iv: iv.toString('hex'),
			encryptedData: encrypted.toString('hex')
		};
	},
	decrypt(text) {
		let iv = Buffer.from(text.iv, 'hex');
		let encryptedText = Buffer.from(text.encryptedData, 'hex');
		let decipher = crypto.createDecipheriv(
			'aes-256-cbc',
			Buffer.from(key),
			iv
		);
		decipher.setAutoPadding(false);
		let decrypted = decipher.update(encryptedText);
		decrypted = Buffer.concat([decrypted, decipher.final()]);
		console.log('TCL: decrypt -> decrypted', decrypted.toString());
		return decrypted.toString();
	},

	s3GetImage(path) {
		return new Promise((resolve, reject) => {
			try {
				const s3bucket = new AWS.S3({
					params: {
						Bucket: process.env.S3_BUCKET_NAME
					}
				});

				const url = s3bucket.getSignedUrl('getObject', {
					Bucket: process.env.S3_BUCKET_NAME,
					Key: path,
					Expires: process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 // time in seconds: e.g. 60 * 5 = 5 mins
				});
				// console.log(url);
				resolve(url);
			} catch (e) {
				console.log('functions3Upload -> e', e);
				reject({ message: 'Could not upload image', err: e });
			}
		});
	},


	generateExpirationToken(email) {
		const date = new Date();

		date.setHours(date.getHours() + 48);
		let token = jwt.sign(
			{ email: email, expiration: date },
			constant.JWTTOKEN.secret,
			{ expiresIn: constant.JWTTOKEN.expiresIn }
		);

		return token;
	},

	async getAdAnalytics(adIds = []) {
        if (!adIds.length) return {};

        const analytics = await UserAdsAnalyticModel.findAll({
            attributes: [
                'adsID',
                [sequelize.fn('SUM', sequelize.col('impressions')), 'total_impressions'],
                [sequelize.fn('SUM', sequelize.col('clicks')), 'total_clicks'],
                [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('userID'))), 'total_reach']
            ],
            where: {
                adsID: { [Op.in]: adIds },
                impressions: { [Op.gt]: 0 }
            },
            group: ['adsID'],
            raw: true
        });

        const analyticsMap = {};
        analytics.forEach(item => {
            analyticsMap[item.adsID] = {
                impressions: parseInt(item.total_impressions) || 0,
                clicks: parseInt(item.total_clicks) || 0,
                reach: parseInt(item.total_reach) || 0
            };
        });

        return analyticsMap;
    },

    async getCampaignReach(adIds = []) {
        if (!adIds.length) return 0;

        const result = await UserAdsAnalyticModel.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('userID'))), 'unique_users']
            ],
            where: {
                adsID: { [Op.in]: adIds },
                impressions: { [Op.gt]: 0 }
            },
            raw: true
        });

        return parseInt(result?.unique_users) || 0;
    },

    formatNumber(num = 0) {
        return num.toLocaleString();
    },

    getAdStatusAndDisplay(ad) {
        const now = new Date();
		console.log(ad.id);
		console.log(ad.start_date_time);
		console.log(ad.end_date_time);
        // Check if date-time fields exist
        if (!ad.start_date_time || !ad.end_date_time) {
			console.log(ad.start_date_time);
			console.log(ad.end_date_time);
			console.log(ad.id);
			console.log('--------======',ad.start_date_time, ad.end_date_time);
            return { 
                status: 'Inactive', 
                display_date: 'Date not available' 
            };
        }

        const startDateTime = new Date(ad.start_date_time.replace(' ', 'T'));
        const endDateTime = new Date(ad.end_date_time.replace(' ', 'T'));

        // Check if dates are valid
        if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {
            return { 
                status: 'Inactive', 
                display_date: 'Invalid date' 
            };
        }

        const formattedStart = startDateTime.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
        const formattedEnd = endDateTime.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

        let status = 'Inactive';

        // First check if ad.status is 'Inactive'
        if (ad.status === 'Inactive') {
            status = 'Inactive';
        } else if (ad.ad_status === 'New') {
            status = 'Under Review';
        } else if (ad.ad_status === 'Rejected') {
            status = 'Rejected';
        } else if (ad.ad_status === 'Approved') {
            if (ad.pause_status) {
                status = 'Paused';
            } else if (now >= startDateTime && now <= endDateTime) {
                status = 'Active';
            } else if (now < startDateTime) {
                status = 'Scheduled';
            } else if (now > endDateTime) {
                status = 'Expired';
            }
        }

        const display_date = status === 'Active'
            ? `${formattedStart} - ongoing`
            : `${formattedStart} - ${formattedEnd}`;

        return { status, display_date };
    }
};
